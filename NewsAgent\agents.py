from crewai import  Agent
from dotenv import load_dotenv
load_dotenv()
import os
from langchain_google_genai import ChatGoogleGenerativeAI
from tools import tool

# Initialize the Google Generative AI model
llm = ChatGoogleGenerativeAI(model="gemini-1.5-flash", 
                             temperature=0.5,
                             verbose=True,
                             google_api_key=os.getenv("GOOGLE_API_KEY")
                                )

# Creating a senior researcher agent with memory and verbose mode
researcher = Agent(
    role="Senior Researcher",
    goal="Uncover ground breaking technologies in {topic}",
    llm=llm,
    memory=True,
    verbose=True,
    backstory=("Driven by curiosity, you're at the forefront of"
    "innovation, eager to explore and share knowledge on the latest advancements in technology."),
    tools=[],
    allow_delegation=True,
)

# Creating a content writer agent with memory and verbose mode
writer = Agent(
    role="Content Writer",
    goal="Craft engaging and informative content on {topic}",
    llm=llm,
    memory=True,
    verbose=True,
    backstory=("With a knack for storytelling, you bring complex ideas to life through captivating narratives."),
    tools=[],
    allow_delegation=False,
)