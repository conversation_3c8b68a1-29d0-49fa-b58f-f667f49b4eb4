from typing_extensions import TypedDict

class State(TypedDict):  # pydantic features
    graph_info: str
    

def start_play(state:State):
    print("Start play node is called")
    return{"graph_info": state["graph_info"] + "I am planning to play"}

def cricket(state:State):
    print("Cricket node is called")
    return{"graph_info": state["graph_info"] + "cricket"}

def badminton(state:State):
    print("Badminton node is called")
    return{"graph_info": state["graph_info"] + "badminton"}

import random 
from typing import Literal

def random_play(state:State)-> Literal["cricket", "badminton"]: # This function is used to randomly select a game and return the literal type
    if random.random() > 0.5:
        return "cricket"
    else:
        return "badminton"

from IPython.display import display, Image
from langgraph.graph import StateGraph,START,END

# Build the graph
graph = StateGraph(State)

# add all the nodes to the graph
graph.add_node("start_play", start_play)
graph.add_node("cricket", cricket)
graph.add_node("badminton", badminton)


# add the edges to the graph
graph.add_edge(START, "start_play")
graph.add_conditional_edges("start_play", random_play)
graph.add_edge("cricket", END)
graph.add_edge("badminton", END)

# compile the graph
graph_builder = graph.compile()  # Ensure the graph is compiled successfully()

# Display the graph
display(graph_builder.get_graph().draw_mermaid_png())

graph_builder.invoke({"graph_info": "My name is John. "})  # Invoke the graph with an initial state